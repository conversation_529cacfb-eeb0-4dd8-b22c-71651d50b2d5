{"languageMap": {"zh": "中文", "en": "英文", "ja": "日文", "ko": "韩文", "fr": "法文", "de": "德文", "es": "西班牙文", "it": "意大利文", "pt": "葡萄牙文", "ru": "俄文", "ar": "阿拉伯文", "hi": "印地文", "th": "泰文", "vi": "越南文", "tr": "土耳其文", "pl": "波兰文", "nl": "荷兰文", "sv": "瑞典文", "da": "丹麦文", "no": "挪威文", "fi": "芬兰文"}, "languageIdentifierMap": {"Chinese": "zh", "chinese": "zh", "zh-cn": "zh", "zh-tw": "zh-tw", "Simplified Chinese": "zh", "Traditional Chinese": "zh-tw", "English": "en", "english": "en", "Japanese": "ja", "japanese": "ja", "Korean": "ko", "korean": "ko", "French": "fr", "french": "fr", "German": "de", "german": "de", "Spanish": "es", "spanish": "es", "Italian": "it", "italian": "it", "Portuguese": "pt", "portuguese": "pt", "Russian": "ru", "russian": "ru", "Arabic": "ar", "arabic": "ar", "Hindi": "hi", "hindi": "hi", "Thai": "th", "thai": "th", "Vietnamese": "vi", "vietnamese": "vi"}, "languageFonts": {"zh": "Microsoft YaHei", "zh-cn": "Microsoft YaHei", "zh-tw": "Microsoft JhengHei", "ja": "MS Gothic", "ko": "Malgun Gothic", "en": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}, "languageEncodings": {"zh": 134, "zh-cn": 134, "zh-tw": 136, "en": 0, "ja": 128, "ko": 129, "ar": 178, "hi": 0, "th": 222, "vi": 163, "ru": 204}, "wrapConfig": {"cjkLanguages": ["zh", "zh-cn", "zh-tw", "ja", "ko"], "zh": {}, "ja": {}, "ko": {}, "western": {}}, "punctuationRules": {"cjk": {"avoidBreakAfter": ["、", "。", "，", "：", "；", "“", "‘", "（", "【", "《", "〈"], "avoidBreakBefore": ["。", "！", "？", "，", "；", "：", "”", "’", "）", "】", "》", "〉", "…"], "preferBreakAfter": ["。", "！", "？", "；"]}, "western": {"avoidBreakAfter": ["a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with"], "avoidBreakBefore": [".", ",", "!", "?", ";", ":", ")", "]", "}"]}}, "assStyleConfig": {"baseStyle": {"fontName": "<PERSON><PERSON>", "primaryColor": "&H0000FFFF", "secondaryColor": "&H0000FFFF", "outlineColor": "&H00000000", "backColor": "&H00000000", "bold": 0, "italic": 0, "underline": 0, "strikeOut": 0, "scaleX": 100, "scaleY": 100, "spacing": 0, "angle": 0, "borderStyle": 1, "outline": 1, "shadow": 1, "alignment": 2}, "resolutionLevels": {"480p": {"baseSize": 18, "marginBase": 25, "description": "标清视频 - 适合小屏设备"}, "720p": {"baseSize": 24, "marginBase": 30, "description": "高清视频 - 平衡清晰度与性能"}, "1080p": {"baseSize": 32, "marginBase": 40, "description": "全高清视频 - 主流标准"}, "1440p": {"baseSize": 48, "marginBase": 50, "description": "2K视频 - 高质量显示"}, "4k": {"baseSize": 64, "marginBase": 60, "description": "4K超高清 - 大屏幕最佳"}, "8k": {"baseSize": 96, "marginBase": 80, "description": "8K超高清 - 专业级显示"}}, "videoTypeOptimization": {"horizontal": {"fontScale": 1.0, "marginScale": 1.0, "sideMargin": 30, "description": "横屏视频 - 标准字体大小，适合宽屏显示"}, "vertical": {"fontScale": 0.75, "marginScale": 1.2, "sideMargin": 15, "description": "竖屏视频 - 缩小字体，增加边距，适合手机竖屏"}, "square": {"fontScale": 0.9, "marginScale": 1.1, "sideMargin": 25, "description": "正方形视频 - 略微缩小字体，平衡显示效果"}}, "languageAdjustments": {"zh": {"fontScale": 1.0, "marginExtra": 8, "verticalExtra": 12, "description": "中文 - 方块字体，需要适当边距"}, "zh-cn": {"fontScale": 1.0, "marginExtra": 8, "verticalExtra": 12, "description": "简体中文 - 继承中文设置"}, "zh-tw": {"fontScale": 1.0, "marginExtra": 8, "verticalExtra": 12, "description": "繁体中文 - 继承中文设置"}, "ja": {"fontScale": 0.95, "marginExtra": 6, "verticalExtra": 10, "description": "日文 - 略小字体，适合假名显示"}, "ko": {"fontScale": 0.98, "marginExtra": 5, "verticalExtra": 8, "description": "韩文 - 韩文字符特性调整"}, "en": {"fontScale": 1.0, "marginExtra": 4, "verticalExtra": 6, "description": "英文 - 拉丁字母，较小边距"}, "ar": {"fontScale": 1.05, "marginExtra": 10, "verticalExtra": 15, "description": "阿拉伯文 - 从右到左，需要更大字体"}, "hi": {"fontScale": 1.02, "marginExtra": 8, "verticalExtra": 12, "description": "印地语 - 天城文字体调整"}, "th": {"fontScale": 1.0, "marginExtra": 6, "verticalExtra": 10, "description": "泰文 - 泰文字符特性"}, "vi": {"fontScale": 1.0, "marginExtra": 5, "verticalExtra": 8, "description": "越南文 - 拉丁字母变体"}, "ru": {"fontScale": 1.0, "marginExtra": 5, "verticalExtra": 8, "description": "俄文 - 西里尔字母"}}, "fontSizeCalculation": {"method": "adaptive", "description": "自适应字体大小计算方法", "rules": {"minFontSize": 12, "maxFontSize": 120, "baseRatio": 0.04, "description": "字体大小 = 视频较小维度 × baseRatio × 各种缩放因子"}, "qualityFactors": {"480p": 0.8, "720p": 1.0, "1080p": 1.0, "1440p": 1.0, "4k": 1.0, "8k": 1.0}, "readabilityOptimization": {"enabled": true, "description": "可读性优化 - 确保字体在不同设备上的可读性", "deviceFactors": {"mobile": 1.1, "tablet": 1.0, "desktop": 0.9, "tv": 0.8}}}, "scriptInfo": {"title": "Video Translation Subtitle", "scriptType": "v4.00+", "wrapStyle": 1, "scaledBorderAndShadow": "yes", "autoAdaptive": false}, "languageWrapStyles": {"zh": 0, "zh-cn": 0, "zh-tw": 0, "ja": 0, "ko": 0, "en": 1, "fr": 1, "de": 1, "es": 1, "it": 1, "pt": 1, "ru": 1, "ar": 0, "hi": 0, "th": 0, "vi": 0, "default": 0}}}